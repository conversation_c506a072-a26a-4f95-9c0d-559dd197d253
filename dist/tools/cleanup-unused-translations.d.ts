import { TranslationIndex } from '../core/translation-index.js';
import { SupportedFramework } from '../types/translation.js';
/**
 * Result of unused translation cleanup analysis
 */
interface UnusedTranslationResult {
    /** Total files scanned */
    totalFilesScanned: number;
    /** Total translation keys found in code */
    totalKeysFoundInCode: number;
    /** Total translation keys in source file */
    totalKeysInSource: number;
    /** Number of unused translation keys */
    unusedKeysCount: number;
    /** Unused translation keys with details */
    unusedKeys: UnusedTranslationKey[];
    /** Used keys for reference */
    usedKeys: string[];
    /** Summary by framework */
    frameworkSummary: Record<string, number>;
    /** Scan configuration used */
    scanConfig: {
        srcDir: string;
        sourceLanguage: string;
        frameworks: SupportedFramework[];
        excludePatterns: string[];
        fileExtensions: string[];
    };
    /** Whether this was a dry run */
    dryRun: boolean;
    /** Deletion results (if not dry run) */
    deletionResults?: DeletionResult[];
}
/**
 * Unused translation key with details
 */
interface UnusedTranslationKey {
    /** The unused translation key */
    key: string;
    /** Value in source language */
    value: any;
    /** Whether this key has nested children */
    hasChildren: boolean;
    /** Child keys if any */
    childKeys?: string[];
    /** Suggested action */
    suggestion: string;
    /** Risk level for deletion */
    riskLevel: 'low' | 'medium' | 'high';
    /** Reason for risk level */
    riskReason: string;
}
/**
 * Result of deletion operation
 */
interface DeletionResult {
    /** Key that was deleted */
    key: string;
    /** Whether deletion was successful */
    success: boolean;
    /** Error message if deletion failed */
    error?: string;
}
/**
 * Setup the cleanup unused translations tool
 */
export declare function setupCleanupUnusedTranslationsTool(server: any, index: TranslationIndex, config: any): void;
/**
 * Cleaner for unused translation keys
 */
export declare class UnusedTranslationCleaner {
    private readonly index;
    private readonly config;
    constructor(index: TranslationIndex, config: any);
    /**
     * Resolve source directory relative to project root or translation directory
     */
    resolveSrcDir(srcDir: string): string;
    /**
     * Find unused translation keys in the source language
     */
    findUnusedTranslations(options: {
        srcDir: string;
        sourceLanguage: string;
        frameworks: SupportedFramework[];
        excludePatterns: string[];
        fileExtensions: string[];
        maxDepth: number;
        dryRun: boolean;
        includeChildren: boolean;
        riskThreshold: 'low' | 'medium' | 'high';
        autoDelete: boolean;
    }): Promise<UnusedTranslationResult>;
    /**
     * Get all source files to scan
     */
    private getSourceFiles;
    /**
     * Get child keys for a given parent key
     */
    private getChildKeys;
    /**
     * Assess the risk level of deleting a translation key
     */
    private assessDeletionRisk;
    /**
     * Check if a value contains dynamic patterns that might indicate programmatic usage
     */
    private containsDynamicPatterns;
    /**
     * Check if a translation key might be used dynamically based on patterns in the codebase
     */
    private checkForDynamicUsage;
    /**
     * Escape special regex characters
     */
    private escapeRegex;
    /**
     * Check if a directory exists
     */
    private directoryExists;
    /**
     * Check if a risk level is acceptable given the threshold
     */
    private isRiskAcceptable;
    /**
     * Generate cleanup suggestion for a key
     */
    private generateCleanupSuggestion;
    /**
     * Perform actual deletion of unused keys
     */
    private performDeletion;
}
export {};
//# sourceMappingURL=cleanup-unused-translations.d.ts.map