/**
 * Test the regex patterns against the actual content
 */

import { promises as fs } from 'fs';

async function testRegexPatterns() {
  console.log('🧪 Testing regex patterns against actual content...\n');

  // Read the actual file content
  const filePath = '/Users/<USER>/Projects/ductize/app/components/dashboard/provider/services/form/ServicesBasicInfo.vue';
  
  try {
    const content = await fs.readFile(filePath, 'utf-8');
    
    console.log('📄 File loaded successfully\n');
    
    // Test patterns for categories
    const escapedPrefix = 'categories';
    
    // Current patterns from our code
    const patterns = [
      // Template literal patterns: t(`prefix.${variable}`) - more flexible matching
      new RegExp('(?:\\$?t|this\\.\\$t)\\s*\\([^)]*`[^`]*' + escapedPrefix + '[^`]*\\$\\{[^}]+\\}[^`]*`[^)]*\\)', 'gms'),
      
      // Vue template patterns: {{ t(`prefix.${variable}`) }}
      new RegExp('\\{\\{[^}]*(?:\\$?t|this\\.\\$t)\\s*\\([^)]*`[^`]*' + escapedPrefix + '[^`]*\\$\\{[^}]+\\}[^`]*`[^)]*\\)[^}]*\\}\\}', 'gms'),
      
      // Simpler pattern - just look for t() with template literal containing categories
      new RegExp('t\\s*\\([^)]*`[^`]*categories[^`]*\\$\\{[^}]+\\}[^`]*`[^)]*\\)', 'gms'),
      
      // Even simpler - just template literal with categories
      new RegExp('`[^`]*categories[^`]*\\$\\{[^}]+\\}[^`]*`', 'gms'),
      
      // Very simple - just categories with template variable
      new RegExp('categories[^`]*\\$\\{[^}]+\\}', 'gms')
    ];
    
    console.log('🔍 Testing patterns:\n');
    
    patterns.forEach((pattern, index) => {
      console.log(`Pattern ${index + 1}: ${pattern.source}`);
      const matches = content.match(pattern);
      if (matches) {
        console.log(`  ✅ Found ${matches.length} matches:`);
        matches.forEach(match => {
          console.log(`    ${match.substring(0, 100)}...`);
        });
      } else {
        console.log(`  ❌ No matches found`);
      }
      console.log('');
    });
    
    // Let's also manually extract the relevant lines
    console.log('📋 Manual extraction of relevant lines:\n');
    const lines = content.split('\n');
    const relevantLines = lines
      .map((line, index) => ({ line: line.trim(), number: index + 1 }))
      .filter(({line}) => /categories.*\$\{/.test(line));
    
    relevantLines.forEach(({line, number}) => {
      console.log(`Line ${number}: ${line}`);
    });
    
    // Test with subCategories too
    console.log('\n🔍 Testing subCategories patterns:\n');
    
    const subCategoriesPatterns = [
      new RegExp('t\\s*\\([^)]*`[^`]*subCategories[^`]*\\$\\{[^}]+\\}[^`]*`[^)]*\\)', 'gms'),
      new RegExp('`[^`]*subCategories[^`]*\\$\\{[^}]+\\}[^`]*`', 'gms'),
      new RegExp('subCategories[^`]*\\$\\{[^}]+\\}', 'gms')
    ];
    
    subCategoriesPatterns.forEach((pattern, index) => {
      console.log(`SubCategories Pattern ${index + 1}: ${pattern.source}`);
      const matches = content.match(pattern);
      if (matches) {
        console.log(`  ✅ Found ${matches.length} matches:`);
        matches.forEach(match => {
          console.log(`    ${match.substring(0, 100)}...`);
        });
      } else {
        console.log(`  ❌ No matches found`);
      }
      console.log('');
    });
    
  } catch (error) {
    console.error('❌ Error reading file:', error.message);
  }
}

// Run the test
testRegexPatterns()
  .then(() => {
    console.log('✅ Regex test completed');
  })
  .catch(error => {
    console.error('❌ Test failed:', error);
  });
