/**
 * Cleanup unused translations from the source of truth translation file
 */
import { z } from 'zod';
import { readdir, stat, readFile, access } from 'fs/promises';
import { join, extname, resolve, isAbsolute } from 'path';
import { TranslationIndex } from '../core/translation-index.js';
import { CodeAnalyzer } from '../core/code-analyzer.js';
import { SupportedFramework, TranslationUsage } from '../types/translation.js';

/**
 * Extended translation usage with file context
 */
interface ExtendedTranslationUsage extends TranslationUsage {
  /** File path where the usage was found */
  filePath?: string;
  /** Detected framework */
  framework?: SupportedFramework;
}

/**
 * Result of unused translation cleanup analysis
 */
interface UnusedTranslationResult {
  /** Total files scanned */
  totalFilesScanned: number;
  /** Total translation keys found in code */
  totalKeysFoundInCode: number;
  /** Total translation keys in source file */
  totalKeysInSource: number;
  /** Number of unused translation keys */
  unusedKeysCount: number;
  /** Unused translation keys with details */
  unusedKeys: UnusedTranslationKey[];
  /** Used keys for reference */
  usedKeys: string[];
  /** Summary by framework */
  frameworkSummary: Record<string, number>;
  /** Scan configuration used */
  scanConfig: {
    srcDir: string;
    sourceLanguage: string;
    frameworks: SupportedFramework[];
    excludePatterns: string[];
    fileExtensions: string[];
  };
  /** Whether this was a dry run */
  dryRun: boolean;
  /** Deletion results (if not dry run) */
  deletionResults?: DeletionResult[];
}

/**
 * Unused translation key with details
 */
interface UnusedTranslationKey {
  /** The unused translation key */
  key: string;
  /** Value in source language */
  value: any;
  /** Whether this key has nested children */
  hasChildren: boolean;
  /** Child keys if any */
  childKeys?: string[];
  /** Suggested action */
  suggestion: string;
  /** Risk level for deletion */
  riskLevel: 'low' | 'medium' | 'high';
  /** Reason for risk level */
  riskReason: string;
}

/**
 * Result of deletion operation
 */
interface DeletionResult {
  /** Key that was deleted */
  key: string;
  /** Whether deletion was successful */
  success: boolean;
  /** Error message if deletion failed */
  error?: string;
}

/**
 * Setup the cleanup unused translations tool
 */
export function setupCleanupUnusedTranslationsTool(
  server: any, 
  index: TranslationIndex, 
  config: any
) {
  server.tool(
    'cleanup_unused_translations',
    'Find and optionally delete translation keys from the source of truth file that are not being used in the codebase. Carefully handles dynamic translations to avoid false positives.',
    {
      srcDir: z.string().optional().describe('Source directory to scan for translation usage. Can be relative (e.g., "app", "./src") or absolute path (e.g., "/Users/<USER>/project/app"). Defaults to config srcDir.'),
      sourceLanguage: z.string().default('en').describe('Source of truth language to cleanup (defaults to "en")'),
      frameworks: z.array(z.enum(['react', 'vue', 'svelte', 'angular'])).optional().describe('Frameworks to analyze. Supported: react, vue, svelte, angular.'),
      excludePatterns: z.array(z.string()).optional().describe('File/directory patterns to exclude from scanning'),
      fileExtensions: z.array(z.string()).optional().describe('File extensions to scan for translation usage'),
      maxDepth: z.number().min(1).max(10).default(5).describe('Maximum directory depth to scan'),
      dryRun: z.boolean().default(true).describe('Preview mode - show what would be deleted without actually deleting'),
      includeChildren: z.boolean().default(false).describe('Include analysis of nested/child keys'),
      riskThreshold: z.enum(['low', 'medium', 'high']).default('medium').describe('Only suggest deletion for keys at or below this risk level'),
      autoDelete: z.boolean().default(false).describe('Automatically delete unused keys (only works when dryRun is false)')
    },
    async ({ 
      srcDir, 
      sourceLanguage,
      frameworks, 
      excludePatterns, 
      fileExtensions,
      maxDepth,
      dryRun,
      includeChildren,
      riskThreshold,
      autoDelete
    }: {
      srcDir?: string;
      sourceLanguage: string;
      frameworks?: SupportedFramework[];
      excludePatterns?: string[];
      fileExtensions?: string[];
      maxDepth: number;
      dryRun: boolean;
      includeChildren: boolean;
      riskThreshold: 'low' | 'medium' | 'high';
      autoDelete: boolean;
    }) => {
      try {
        // Validate frameworks and provide helpful messages
        const supportedFrameworks = ['react', 'vue', 'svelte', 'angular'];
        const requestedFrameworks = frameworks || ['react', 'vue', 'svelte', 'angular'];
        const unsupportedFrameworks = requestedFrameworks.filter(f => !supportedFrameworks.includes(f));

        if (unsupportedFrameworks.length > 0) {
          const frameworkMappings: Record<string, string> = {
            'nuxt': 'vue (Nuxt uses Vue files)',
            'next': 'react (Next.js uses React)',
            'nextjs': 'react (Next.js uses React)',
            'gatsby': 'react (Gatsby uses React)',
            'vite': 'Use the underlying framework (react/vue/svelte)',
            'webpack': 'Use the underlying framework (react/vue/svelte)'
          };

          const suggestions = unsupportedFrameworks.map(f => {
            const suggestion = frameworkMappings[f.toLowerCase()];
            return suggestion ? `"${f}" -> use "${suggestion}"` : `"${f}" is not supported`;
          }).join(', ');

          return {
            content: [{
              type: 'text',
              text: JSON.stringify({
                error: 'Unsupported framework(s) detected',
                unsupportedFrameworks,
                supportedFrameworks,
                suggestions,
                message: `Please use one of the supported frameworks: ${supportedFrameworks.join(', ')}`
              }, null, 2)
            }]
          };
        }

        const cleaner = new UnusedTranslationCleaner(index, config);

        // Resolve source directory relative to project root or translation directory
        const resolvedSrcDir = cleaner.resolveSrcDir(srcDir || config.srcDir || './src');

        const result = await cleaner.findUnusedTranslations({
          srcDir: resolvedSrcDir,
          sourceLanguage,
          frameworks: requestedFrameworks as SupportedFramework[],
          excludePatterns: excludePatterns || ['node_modules', 'dist', 'build', '.git'],
          fileExtensions: fileExtensions || ['.ts', '.tsx', '.js', '.jsx', '.vue', '.svelte', '.html'],
          maxDepth,
          dryRun,
          includeChildren,
          riskThreshold,
          autoDelete
        });

        return {
          content: [{
            type: 'text',
            text: JSON.stringify(result, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: 'text',
            text: `Error cleaning up unused translations: ${error instanceof Error ? error.message : 'Unknown error'}`
          }]
        };
      }
    }
  );
}

/**
 * Cleaner for unused translation keys
 */
export class UnusedTranslationCleaner {
  constructor(
    private readonly index: TranslationIndex,
    private readonly config: any
  ) {}

  /**
   * Resolve source directory relative to project root or translation directory
   */
  resolveSrcDir(srcDir: string): string {
    // If it's already an absolute path, use it as-is
    if (isAbsolute(srcDir)) {
      if (this.config.debug) {
        console.log(`🔍 Using absolute srcDir: ${srcDir}`);
      }
      return srcDir;
    }

    let resolvedPath: string;

    // Try to resolve relative to project root first
    if (this.config.projectRoot) {
      resolvedPath = resolve(this.config.projectRoot, srcDir);
      if (this.config.debug) {
        console.log(`🔍 Resolved srcDir relative to projectRoot (${this.config.projectRoot}): ${srcDir} -> ${resolvedPath}`);
      }
      return resolvedPath;
    }

    // Fallback: resolve relative to translation directory parent
    if (this.config.translationDir) {
      const translationDirParent = resolve(this.config.translationDir, '..');
      resolvedPath = resolve(translationDirParent, srcDir);
      if (this.config.debug) {
        console.log(`🔍 Resolved srcDir relative to translation dir parent (${translationDirParent}): ${srcDir} -> ${resolvedPath}`);
      }
      return resolvedPath;
    }

    // Last resort: resolve relative to current working directory
    resolvedPath = resolve(process.cwd(), srcDir);
    if (this.config.debug) {
      console.log(`🔍 Resolved srcDir relative to cwd (${process.cwd()}): ${srcDir} -> ${resolvedPath}`);
    }
    return resolvedPath;
  }

  /**
   * Find unused translation keys in the source language
   */
  async findUnusedTranslations(options: {
    srcDir: string;
    sourceLanguage: string;
    frameworks: SupportedFramework[];
    excludePatterns: string[];
    fileExtensions: string[];
    maxDepth: number;
    dryRun: boolean;
    includeChildren: boolean;
    riskThreshold: 'low' | 'medium' | 'high';
    autoDelete: boolean;
  }): Promise<UnusedTranslationResult> {
    const {
      srcDir,
      sourceLanguage,
      frameworks,
      excludePatterns,
      fileExtensions,
      maxDepth,
      dryRun,
      includeChildren,
      riskThreshold,
      autoDelete
    } = options;

    // Get all source files
    const sourceFiles = await this.getSourceFiles(srcDir, fileExtensions, excludePatterns, maxDepth);

    if (sourceFiles.length === 0) {
      // Provide detailed error information with path resolution details
      const originalSrcDir = options.srcDir || this.config.srcDir || './src';
      const isAbsolutePath = isAbsolute(originalSrcDir);
      const dirExists = await this.directoryExists(srcDir);

      const pathResolutionInfo = [
        `📁 Path Resolution:`,
        `  - Original srcDir: "${originalSrcDir}" ${isAbsolutePath ? '(absolute)' : '(relative)'}`,
        `  - Resolved to: "${srcDir}"`,
        `  - Directory exists: ${dirExists}`,
        ``,
        `🔧 Resolution Process:`,
        this.config.projectRoot ? `  - Project root: ${this.config.projectRoot}` : '  - Project root: not set',
        this.config.translationDir ? `  - Translation dir: ${this.config.translationDir}` : '  - Translation dir: not set',
        `  - Current working dir: ${process.cwd()}`,
        ``,
        `📋 Scan Configuration:`,
        `  - File extensions: ${fileExtensions.join(', ')}`,
        `  - Exclude patterns: ${excludePatterns.join(', ')}`,
        `  - Max depth: ${maxDepth}`,
        ``,
        `💡 Suggestions:`,
        `  1. Ensure the source directory exists and contains files with the specified extensions`,
        `  2. Use absolute paths (e.g., "/Users/<USER>/project/app") for clarity`,
        `  3. Or use relative paths from your project root (e.g., "./app", "src/components")`,
        `  4. Check that file extensions match your project structure`,
        `  5. Verify exclude patterns aren't filtering out your source directory`
      ].join('\n');

      throw new Error(`No source files found!\n\n${pathResolutionInfo}`);
    }

    // Analyze each file for translation usage
    const analyzer = new CodeAnalyzer(frameworks);
    const allUsedKeys = new Set<string>();
    const frameworkCounts: Record<string, number> = {};

    for (const filePath of sourceFiles) {
      try {
        const analysisResult = await analyzer.analyzeFile(filePath, {
          extractHardcoded: false,
          findUsage: true,
          translationIndex: this.index,
          minStringLength: 1,
          excludePatterns: []
        });

        // Count framework usage
        if (analysisResult.detectedFramework) {
          frameworkCounts[analysisResult.detectedFramework] =
            (frameworkCounts[analysisResult.detectedFramework] || 0) + 1;
        }

        // Collect all used translation keys (both existing and missing)
        for (const usage of analysisResult.translationUsage) {
          allUsedKeys.add(usage.keyPath);

          // Also add parent keys if includeChildren is enabled
          if (includeChildren) {
            const parts = usage.keyPath.split('.');
            for (let i = 1; i < parts.length; i++) {
              const parentKey = parts.slice(0, i).join('.');
              allUsedKeys.add(parentKey);
            }
          }
        }
      } catch (error) {
        if (this.config.debug) {
          console.warn(`Failed to analyze file ${filePath}:`, error);
        }
        // Continue with other files
      }
    }

    // Get all keys from the source language
    const allSourceKeys = this.index.getKeys().filter(key =>
      this.index.has(key, sourceLanguage)
    );

    // Find unused keys
    const unusedKeys: UnusedTranslationKey[] = [];
    const usedKeys: string[] = Array.from(allUsedKeys);

    for (const key of allSourceKeys) {
      if (!allUsedKeys.has(key)) {
        // Check if this key has children
        const childKeys = includeChildren ? this.getChildKeys(key, allSourceKeys) : [];
        const hasChildren = childKeys.length > 0;

        // Skip if any child is used (unless includeChildren is false)
        if (hasChildren && !includeChildren) {
          const hasUsedChild = childKeys.some(childKey => allUsedKeys.has(childKey));
          if (hasUsedChild) continue;
        }

        const value = this.index.get(key, sourceLanguage);

        // Check for dynamic usage patterns in the codebase
        const dynamicCheck = await this.checkForDynamicUsage(key, sourceFiles, frameworks);

        // If dynamic usage is detected, consider this key as "used" and skip it
        if (dynamicCheck.isDynamic) {
          if (this.config.debug) {
            console.log(`🔍 Skipping ${key} - detected dynamic usage: ${dynamicCheck.evidence[0]}`);
          }
          allUsedKeys.add(key); // Add to used keys
          continue; // Skip this key entirely
        }

        const riskAssessment = this.assessDeletionRisk(key, value, hasChildren, childKeys, dynamicCheck);

        // Only include keys at or below the risk threshold
        if (this.isRiskAcceptable(riskAssessment.riskLevel, riskThreshold)) {
          unusedKeys.push({
            key,
            value: value?.value,
            hasChildren,
            childKeys: hasChildren ? childKeys : undefined,
            suggestion: this.generateCleanupSuggestion(key, hasChildren, childKeys.length),
            riskLevel: riskAssessment.riskLevel,
            riskReason: riskAssessment.reason
          });
        }
      }
    }

    // Sort by risk level and then by key name
    unusedKeys.sort((a, b) => {
      const riskOrder = { low: 0, medium: 1, high: 2 };
      const riskDiff = riskOrder[a.riskLevel] - riskOrder[b.riskLevel];
      return riskDiff !== 0 ? riskDiff : a.key.localeCompare(b.key);
    });

    const result: UnusedTranslationResult = {
      totalFilesScanned: sourceFiles.length,
      totalKeysFoundInCode: allUsedKeys.size,
      totalKeysInSource: allSourceKeys.length,
      unusedKeysCount: unusedKeys.length,
      unusedKeys,
      usedKeys: usedKeys.sort(),
      frameworkSummary: frameworkCounts,
      scanConfig: {
        srcDir,
        sourceLanguage,
        frameworks,
        excludePatterns,
        fileExtensions
      },
      dryRun
    };

    // Perform actual deletion if requested
    if (!dryRun && autoDelete && unusedKeys.length > 0) {
      result.deletionResults = await this.performDeletion(unusedKeys, sourceLanguage);
    }

    return result;
  }

  /**
   * Get all source files to scan
   */
  private async getSourceFiles(
    srcDir: string,
    extensions: string[],
    excludePatterns: string[],
    maxDepth: number
  ): Promise<string[]> {
    const files: string[] = [];
    const excludeRegexes = excludePatterns.map(pattern => new RegExp(pattern));

    const scanDirectory = async (dir: string, currentDepth: number): Promise<void> => {
      if (currentDepth > maxDepth) return;

      try {
        const entries = await readdir(dir);

        for (const entry of entries) {
          const fullPath = join(dir, entry);

          // Check if path should be excluded
          if (excludeRegexes.some(regex => regex.test(fullPath))) {
            continue;
          }

          const stats = await stat(fullPath);

          if (stats.isDirectory()) {
            await scanDirectory(fullPath, currentDepth + 1);
          } else if (stats.isFile()) {
            const ext = extname(entry);
            if (extensions.includes(ext)) {
              files.push(fullPath);
            }
          }
        }
      } catch (error) {
        if (this.config.debug) {
          console.warn(`Failed to scan directory ${dir}:`, error);
        }
      }
    };

    await scanDirectory(srcDir, 0);
    return files;
  }

  /**
   * Get child keys for a given parent key
   */
  private getChildKeys(parentKey: string, allKeys: string[]): string[] {
    const prefix = parentKey + '.';
    return allKeys.filter(key =>
      key.startsWith(prefix) &&
      key !== parentKey
    );
  }

  /**
   * Assess the risk level of deleting a translation key
   */
  private assessDeletionRisk(
    key: string,
    value: any,
    hasChildren: boolean,
    childKeys: string[],
    dynamicCheck?: { isDynamic: boolean; evidence: string[] }
  ): { riskLevel: 'low' | 'medium' | 'high'; reason: string } {
    // Highest risk: Dynamic usage detected in codebase
    if (dynamicCheck?.isDynamic) {
      return {
        riskLevel: 'high',
        reason: `Potentially used dynamically in code: ${dynamicCheck.evidence[0] || 'Dynamic patterns detected'}`
      };
    }

    // High risk indicators
    if (hasChildren && childKeys.length > 5) {
      return {
        riskLevel: 'high',
        reason: `Parent key with many children (${childKeys.length} child keys)`
      };
    }

    if (typeof value?.value === 'string' && this.containsDynamicPatterns(value.value)) {
      return {
        riskLevel: 'high',
        reason: 'Contains dynamic patterns that might be used programmatically'
      };
    }

    // Medium risk indicators
    if (hasChildren) {
      return {
        riskLevel: 'medium',
        reason: `Parent key with ${childKeys.length} child keys`
      };
    }

    if (key.includes('common') || key.includes('shared') || key.includes('global')) {
      return {
        riskLevel: 'medium',
        reason: 'Key suggests shared/common usage across the application'
      };
    }

    if (key.split('.').length === 1) {
      return {
        riskLevel: 'medium',
        reason: 'Top-level key might be used as namespace'
      };
    }

    // Low risk - safe to delete
    return {
      riskLevel: 'low',
      reason: 'Leaf key with no apparent dynamic usage'
    };
  }

  /**
   * Check if a value contains dynamic patterns that might indicate programmatic usage
   */
  private containsDynamicPatterns(value: string): boolean {
    // Template literal patterns
    if (/\$\{[^}]+\}/.test(value)) return true;

    // Handlebars/Vue/Angular patterns
    if (/\{\{[^}]+\}\}/.test(value)) return true;

    // Interpolation patterns
    if (/\{[^}]+\}/.test(value)) return true;

    // Common dynamic indicators
    if (/(count|number|index|id|name|type)/.test(value.toLowerCase())) return true;

    return false;
  }

  /**
   * Check if a translation key might be used dynamically based on patterns in the codebase
   */
  private async checkForDynamicUsage(
    key: string,
    sourceFiles: string[],
    frameworks: SupportedFramework[]
  ): Promise<{ isDynamic: boolean; evidence: string[] }> {
    const evidence: string[] = [];
    const keyParts = key.split('.');
    const escapedKey = this.escapeRegex(key);
    const escapedFirstPart = this.escapeRegex(keyParts[0] || key);
    const escapedLastPart = this.escapeRegex(key.split('.').pop() || key);

    // Create patterns to look for dynamic usage of this key or its parts
    const dynamicPatterns = [
      // Direct key usage (handles multiline, whitespace, and quotes) - MOST IMPORTANT
      new RegExp('t\\s*\\(\\s*[\'"`]' + this.escapeRegex(key) + '[\'"`]', 'gms'),

      // Template literal patterns: t(`prefix.${variable}.suffix`) with multiline support
      new RegExp('t\\s*\\(\\s*`[^`]*\\$\\{[^}]+\\}[^`]*' + escapedLastPart + '[^`]*`', 'gms'),
      new RegExp('t\\s*\\(\\s*`[^`]*' + escapedFirstPart + '[^`]*\\$\\{[^}]+\\}[^`]*`', 'gms'),

      // String concatenation: t(prefix + variable + suffix) with multiline support
      new RegExp('t\\s*\\([^)]*[\'"`]' + escapedFirstPart + '[\'"`][^)]*\\+[^)]*\\)', 'gms'),
      new RegExp('t\\s*\\([^)]*\\+[^)]*[\'"`][^\'"`]*' + escapedLastPart + '[\'"`][^)]*\\)', 'gms'),

      // Variable construction: const key = `prefix.${id}.suffix`; t(key) with multiline support
      new RegExp('[\'"`][^\'"`]*' + escapedFirstPart + '[^\'"`]*\\$\\{[^}]+\\}[^\'"`]*[\'"`]', 'gms'),

      // Object property access: translations[category][id] with multiline support
      new RegExp('\\b' + escapedFirstPart + '\\s*\\[\\s*[^\\]]+\\s*\\]', 'gms'),

      // Dynamic key building with join: [prefix, variable, suffix].join('.') with multiline support
      new RegExp('\\[[^\\]]*[\'"`]' + escapedFirstPart + '[\'"`][^\\]]*\\]\\.join\\s*\\(\\s*[\'"`]\\.[\'"`]\\s*\\)', 'gms'),

      // Component prop patterns: description="translation.key" or :description="translation.key"
      new RegExp('(?:title|label|placeholder|alt|aria-label|description|text|message|content)\\s*=\\s*[\'"`]' + this.escapeRegex(key) + '[\'"`]', 'gms'),
      new RegExp(':(?:title|label|placeholder|alt|aria-label|description|text|message|content)\\s*=\\s*[\'"`]' + this.escapeRegex(key) + '[\'"`]', 'gms'),

      // Vue/React/Angular framework-specific patterns
      new RegExp('\\$t\\s*\\(\\s*[\'"`]' + this.escapeRegex(key) + '[\'"`]', 'gms'),
      new RegExp('this\\.\\$t\\s*\\(\\s*[\'"`]' + this.escapeRegex(key) + '[\'"`]', 'gms'),
      new RegExp('translate\\.(?:get|instant)\\s*\\(\\s*[\'"`]' + this.escapeRegex(key) + '[\'"`]', 'gms'),

      // Template literal with exact key match
      new RegExp('t\\s*\\(\\s*`' + this.escapeRegex(key) + '`', 'gms'),
      new RegExp('\\$t\\s*\\(\\s*`' + this.escapeRegex(key) + '`', 'gms')
    ];

    for (const filePath of sourceFiles.slice(0, 50)) { // Limit to first 50 files for performance
      try {
        const content = await readFile(filePath, 'utf-8');

        for (const pattern of dynamicPatterns) {
          const matches = content.match(pattern);
          if (matches) {
            evidence.push(`Found dynamic pattern in ${filePath}: ${matches[0].substring(0, 100)}...`);
          }
        }

        // Also check for the key parts being used in variable names or object properties
        for (const part of keyParts) {
          if (part.length > 3) { // Only check meaningful parts
            const variablePattern = new RegExp('\\b' + this.escapeRegex(part) + '\\b.*=.*[\'"`][^\'"`]*\\$\\{', 'g');
            const matches = content.match(variablePattern);
            if (matches) {
              evidence.push(`Found potential dynamic usage of "${part}" in ${filePath}`);
            }
          }
        }
      } catch (error) {
        // Continue with other files
      }
    }

    return {
      isDynamic: evidence.length > 0,
      evidence
    };
  }

  /**
   * Escape special regex characters
   */
  private escapeRegex(str: string): string {
    return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * Check if a directory exists
   */
  private async directoryExists(dirPath: string): Promise<boolean> {
    try {
      const stats = await stat(dirPath);
      return stats.isDirectory();
    } catch {
      return false;
    }
  }

  /**
   * Check if a risk level is acceptable given the threshold
   */
  private isRiskAcceptable(riskLevel: 'low' | 'medium' | 'high', threshold: 'low' | 'medium' | 'high'): boolean {
    const riskOrder = { low: 0, medium: 1, high: 2 };
    return riskOrder[riskLevel] <= riskOrder[threshold];
  }

  /**
   * Generate cleanup suggestion for a key
   */
  private generateCleanupSuggestion(key: string, hasChildren: boolean, childCount: number): string {
    if (hasChildren) {
      return `Delete parent key "${key}" and its ${childCount} child keys`;
    }
    return `Delete unused key "${key}"`;
  }

  /**
   * Perform actual deletion of unused keys
   */
  private async performDeletion(unusedKeys: UnusedTranslationKey[], sourceLanguage: string): Promise<DeletionResult[]> {
    const results: DeletionResult[] = [];

    for (const unusedKey of unusedKeys) {
      try {
        // Use the existing delete functionality from the index
        this.index.delete(unusedKey.key, sourceLanguage);

        results.push({
          key: unusedKey.key,
          success: true
        });
      } catch (error) {
        results.push({
          key: unusedKey.key,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return results;
  }
}
